'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import { createClient } from '@/lib/supabase/client';
import type { Database } from '@/lib/supabase/database.types';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { User, Package, Settings, LogOut, MapPin, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';
import type { User as SupabaseUser } from '@supabase/supabase-js';

export default function AccountPage() {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);
  type OrderSummary = Pick<Database['public']['Tables']['orders']['Row'], 'id' | 'order_number' | 'created_at' | 'status' | 'total_amount'>;
  const [orders, setOrders] = useState<OrderSummary[]>([]);
  type UserAddress = Database['public']['Tables']['user_addresses']['Row'];
  const [addresses, setAddresses] = useState<UserAddress[]>([]);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [addressFormData, setAddressFormData] = useState({
    type: 'shipping' as 'billing' | 'shipping',
    first_name: '',
    last_name: '',
    company: '',
    street_address: '',
    city: '',
    postal_code: '',
    country: 'CH',
    is_default: false
  });
  const [savingAddress, setSavingAddress] = useState(false);
  const [addressError, setAddressError] = useState<string | null>(null);
  const [addressSuccess, setAddressSuccess] = useState<string | null>(null);
  type LoyaltyInfo = {
    currentLevel: number;
    currentLevelName: string;
    totalPoints: number;
    lifetimeSpend: number;
    pointsToNext: number;
    nextLevelName: string | null;
    discountPercent: number;
    pointsMultiplier: number;
    pointsToNextGift: number;
    nextGiftThreshold: number | null;
  };
  const [loyalty, setLoyalty] = useState<LoyaltyInfo | null>(null);
  const [loyaltyLoading, setLoyaltyLoading] = useState(true);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const router = useRouter();
  // Persist supabase client instance across renders to avoid effect loops
  const supabaseRef = useRef(createClient());
  const supabase = supabaseRef.current;
  const t = useTranslations('account');
  const tNav = useTranslations('navigation');
  const locale = useLocale();

  // Check current user once and subscribe to future auth state changes
  useEffect(() => {
    const checkCurrentUser = async () => {
      try {
        const { data, error } = await supabase.auth.getUser();
        if (error) {
          console.error('Error fetching current user:', error.message);
        }
        const current = data?.user ?? null;
        setUser(current);
        if (!current) {
          router.push(`/${locale}/login`);
        }
      } catch (err) {
        console.error('Unexpected error getting user:', err);
      } finally {
        setLoading(false);
      }
    };

    checkCurrentUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, locale]);

  // Load loyalty + orders when user is available
  useEffect(() => {
    if (!user) return;

    const loadOrders = async () => {
      try {
        setOrdersLoading(true);
        const { data, error } = await supabase
          .from('orders')
          .select('id, order_number, created_at, status, total_amount')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching user orders:', error);
        } else {
          setOrders(data || []);
        }
      } catch (err) {
        console.error('Unexpected error loading orders:', err);
      } finally {
        setOrdersLoading(false);
      }
    };

    const loadLoyalty = async () => {
      try {
        setLoyaltyLoading(true);
        const res = await fetch('/api/account/loyalty');
        if (!res.ok) throw new Error('Failed to fetch loyalty');
        const data: LoyaltyInfo = await res.json();
        setLoyalty(data);
      } catch (err) {
        console.error('Error fetching loyalty:', err);
      } finally {
        setLoyaltyLoading(false);
      }
    };

    loadOrders();
    loadLoyalty();
    loadAddresses();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const loadAddresses = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('user_addresses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching addresses:', error);
      } else {
        setAddresses(data || []);
      }
    } catch (err) {
      console.error('Error loading addresses:', err);
    }
  };

  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setSavingAddress(true);
    setAddressError(null);
    setAddressSuccess(null);

    try {
      const { error } = await supabase
        .from('user_addresses')
        .insert({
          user_id: user.id,
          ...addressFormData
        });

      if (error) {
        console.error('Error saving address:', error);
        setAddressError('Errore nel salvare l\'indirizzo. Riprova.');
        return;
      }

      // Reset form and refresh addresses
      setAddressFormData({
        type: 'shipping',
        first_name: '',
        last_name: '',
        company: '',
        street_address: '',
        city: '',
        postal_code: '',
        country: 'CH',
        is_default: false
      });
      setShowAddressForm(false);
      setAddressSuccess('Indirizzo salvato con successo!');
      loadAddresses();
    } catch (error) {
      console.error('Error saving address:', error);
      setAddressError('Errore nel salvare l\'indirizzo. Riprova.');
    } finally {
      setSavingAddress(false);
    }
  };

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push(`/${locale}`);
  };

  const handleUpdateProfile = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setUpdating(true);

    try {
      // Here you would typically update user profile data
      toast.success(t('profile.success'));
    } catch {
      toast.error('Fehler beim Aktualisieren des Profils');
    } finally {
      setUpdating(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (newPassword !== confirmPassword) {
      toast.error('Passwörter stimmen nicht überein');
      return;
    }
    if (newPassword.length < 6) {
      toast.error('Passwort muss mindestens 6 Zeichen lang sein');
      return;
    }

    setChangingPassword(true);
    try {
      const { error } = await supabase.auth.updateUser({ password: newPassword });
      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Passwort erfolgreich aktualisiert');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch {
      toast.error('Fehler beim Aktualisieren des Passworts');
    } finally {
      setChangingPassword(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/4"></div>
            <div className="h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">{t('title')}</h1>
            <p className="text-muted-foreground">{t('description')}</p>
          </div>
          <Button variant="outline" onClick={handleSignOut}>
            <LogOut className="mr-2 h-4 w-4" />
            {tNav('logout')}
          </Button>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">
              <User className="mr-2 h-4 w-4" />
              {t('profile.title')}
            </TabsTrigger>
            <TabsTrigger value="orders">
              <Package className="mr-2 h-4 w-4" />
              {t('orders.title')}
            </TabsTrigger>
            <TabsTrigger value="addresses">
              <MapPin className="mr-2 h-4 w-4" />
              {t('addresses.title')}
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="mr-2 h-4 w-4" />
              {t('security.title')}
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>{t('profile.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Loyalty section */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <User className="mr-2 h-5 w-5" /> {t('loyalty.title')}
                  </h3>
                  {loyaltyLoading ? (
                    <div className="h-6 bg-muted rounded w-1/2 animate-pulse" />
                  ) : loyalty ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="p-4 border rounded-lg bg-muted/50 text-center">
                          <p className="text-sm text-muted-foreground">{t('loyalty.currentLevel')}</p>
                          <p className="text-xl font-bold">{loyalty.currentLevelName}</p>
                          <p className="text-xs text-muted-foreground">Livello {loyalty.currentLevel}</p>
                        </div>
                        <div className="p-4 border rounded-lg bg-muted/50 text-center">
                          <p className="text-sm text-muted-foreground">{t('loyalty.totalPoints')}</p>
                          <p className="text-xl font-bold">{loyalty.totalPoints}</p>
                          <p className="text-xs text-muted-foreground">punti totali</p>
                        </div>
                        <div className="p-4 border rounded-lg bg-muted/50 text-center">
                          <p className="text-sm text-muted-foreground">{t('loyalty.pointsToNext')}</p>
                          <p className="text-xl font-bold">{loyalty.pointsToNext}</p>
                          <p className="text-xs text-muted-foreground">
                            {loyalty.nextLevelName ? `per ${loyalty.nextLevelName}` : 'livello massimo'}
                          </p>
                        </div>
                        <div className="p-4 border rounded-lg bg-muted/50 text-center">
                          <p className="text-sm text-muted-foreground">Sconto attuale</p>
                          <p className="text-xl font-bold">{loyalty.discountPercent}%</p>
                          <p className="text-xs text-muted-foreground">sui tuoi acquisti</p>
                        </div>
                      </div>
                      <div className="p-4 border rounded-lg bg-gradient-to-r from-blue-50 to-purple-50">
                        <h4 className="font-semibold mb-2">{t('loyalty.lifetimeStats')}</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">{t('loyalty.totalSpent')}</p>
                            <p className="font-semibold">{formatCurrency(loyalty.lifetimeSpend)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">{t('loyalty.pointsEarned')}</p>
                            <p className="font-semibold">{loyalty.totalPoints} punti</p>
                          </div>
                        </div>
                      </div>
                      {loyalty.pointsToNextGift > 0 && (
                        <div className="p-4 border rounded-lg bg-gradient-to-r from-green-50 to-emerald-50">
                          <h4 className="font-semibold mb-2">🎁 Prossimo regalo</h4>
                          <div className="text-sm">
                            <p className="text-muted-foreground">
                              Ti mancano <span className="font-bold text-green-600">{loyalty.pointsToNextGift} punti</span> per il prossimo regalo
                            </p>
                            {loyalty.nextGiftThreshold && (
                              <p className="text-xs text-muted-foreground mt-1">
                                (spesa equivalente: {formatCurrency(loyalty.nextGiftThreshold)})
                              </p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">-</p>
                  )}
                </div>
                <form onSubmit={handleUpdateProfile} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="email">{t('profile.email')}</Label>
                      <Input
                        id="email"
                        type="email"
                        value={user.email || ''}
                        disabled
                        className="bg-muted"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">{t('profile.phone')}</Label>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="+41 44 123 45 67"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">{t('profile.firstName')}</Label>
                      <Input
                        id="firstName"
                        placeholder="Max"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">{t('profile.lastName')}</Label>
                      <Input
                        id="lastName"
                        placeholder="Mustermann"
                      />
                    </div>
                  </div>
                  <Button type="submit" disabled={updating}>
                    {updating ? '...' : t('profile.save')}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>{t('orders.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              {ordersLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
                </div>
              ) : orders.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">{t('orders.orderNumber')}</th>
                        <th className="text-left py-3 px-4">{t('orders.date')}</th>
                        <th className="text-left py-3 px-4">{t('orders.status')}</th>
                        <th className="text-left py-3 px-4">{t('orders.total')}</th>
                        <th className="w-4" />
                      </tr>
                    </thead>
                    <tbody>
                      {orders.map(order => (
                        <tr
                          key={order.id}
                          className="border-b hover:bg-muted/50 cursor-pointer"
                          onClick={() => router.push(`/${locale}/account/orders/${order.id}`)}
                        >
                          <td className="py-3 px-4 font-mono">#{order.order_number || order.id.slice(0, 8)}</td>
                          <td className="py-3 px-4">
                            {new Date(order.created_at).toLocaleDateString(locale === 'it' ? 'it-CH' : locale === 'fr' ? 'fr-CH' : 'de-CH')}
                          </td>
                          <td className="py-3 px-4">
                            <Badge className={`${getStatusColor(order.status)} w-fit`}>{order.status}</Badge>
                          </td>
                          <td className="py-3 px-4 font-semibold">{formatCurrency(order.total_amount)}</td>
                           <td className="py-3 px-4 text-right">
                             <ChevronRight className="h-4 w-4 text-muted-foreground" />
                           </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Package className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{t('orders.noOrders')}</h3>
                  <p className="text-muted-foreground mb-4">
                    {t('orders.noOrdersDescription')}
                  </p>
                  <Button asChild>
                    <a href={`/${locale}/shop`}>Jetzt einkaufen</a>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
          </TabsContent>

          {/* Addresses Tab */}
          <TabsContent value="addresses">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {t('addresses.title')}
                  <Button onClick={() => setShowAddressForm(true)}>
                    {t('addresses.addNew')}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {addressSuccess && (
                  <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                    {addressSuccess}
                  </div>
                )}
                {addressError && (
                  <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    {addressError}
                  </div>
                )}
                {addresses.length > 0 ? (
                  <div className="space-y-4">
                    {addresses.map((address) => (
                      <div key={address.id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={address.type === 'billing' ? 'default' : 'secondary'}>
                                {address.type === 'billing' ? 'Fatturazione' : 'Spedizione'}
                              </Badge>
                              {address.is_default && (
                                <Badge variant="outline">Predefinito</Badge>
                              )}
                            </div>
                            <p className="font-medium">
                              {address.first_name} {address.last_name}
                            </p>
                            {address.company && (
                              <p className="text-sm text-muted-foreground">{address.company}</p>
                            )}
                            <p className="text-sm">{address.street_address}</p>
                            <p className="text-sm">
                              {address.postal_code} {address.city}
                            </p>
                            <p className="text-sm">{address.country}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <MapPin className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{t('addresses.emptyTitle')}</h3>
                    <p className="text-muted-foreground mb-4">
                      {t('addresses.emptyDescription')}
                    </p>
                    <Button onClick={() => setShowAddressForm(true)}>
                      {t('addresses.addNew')}
                    </Button>
                  </div>
                )}

                {/* Address Form */}
                {showAddressForm && (
                  <div className="mt-6 border rounded-lg p-4">
                    <h3 className="text-lg font-semibold mb-4">Aggiungi nuovo indirizzo</h3>
                    <form onSubmit={handleAddressSubmit} className="space-y-4">
                      <div>
                        <Label htmlFor="type">Tipo</Label>
                        <select
                          id="type"
                          value={addressFormData.type}
                          onChange={(e) => setAddressFormData({
                            ...addressFormData,
                            type: e.target.value as 'billing' | 'shipping'
                          })}
                          className="w-full p-2 border rounded mt-1"
                        >
                          <option value="shipping">Spedizione</option>
                          <option value="billing">Fatturazione</option>
                        </select>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="first_name">Nome *</Label>
                          <Input
                            id="first_name"
                            value={addressFormData.first_name}
                            onChange={(e) => setAddressFormData({
                              ...addressFormData,
                              first_name: e.target.value
                            })}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="last_name">Cognome *</Label>
                          <Input
                            id="last_name"
                            value={addressFormData.last_name}
                            onChange={(e) => setAddressFormData({
                              ...addressFormData,
                              last_name: e.target.value
                            })}
                            required
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="company">Azienda</Label>
                        <Input
                          id="company"
                          value={addressFormData.company}
                          onChange={(e) => setAddressFormData({
                            ...addressFormData,
                            company: e.target.value
                          })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="street_address">Indirizzo *</Label>
                        <Input
                          id="street_address"
                          value={addressFormData.street_address}
                          onChange={(e) => setAddressFormData({
                            ...addressFormData,
                            street_address: e.target.value
                          })}
                          required
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="postal_code">CAP *</Label>
                          <Input
                            id="postal_code"
                            value={addressFormData.postal_code}
                            onChange={(e) => setAddressFormData({
                              ...addressFormData,
                              postal_code: e.target.value
                            })}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="city">Città *</Label>
                          <Input
                            id="city"
                            value={addressFormData.city}
                            onChange={(e) => setAddressFormData({
                              ...addressFormData,
                              city: e.target.value
                            })}
                            required
                          />
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="is_default"
                          checked={addressFormData.is_default}
                          onChange={(e) => setAddressFormData({
                            ...addressFormData,
                            is_default: e.target.checked
                          })}
                        />
                        <Label htmlFor="is_default">Imposta come predefinito</Label>
                      </div>
                      <div className="flex gap-2 pt-4">
                        <Button type="submit" disabled={savingAddress}>
                          {savingAddress ? 'Salvataggio...' : 'Salva'}
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setShowAddressForm(false)}
                        >
                          Annulla
                        </Button>
                      </div>
                    </form>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <div className="space-y-6">
              {/* Change Password */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('security.changePassword')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleChangePassword} className="space-y-4 max-w-sm">
                    <div>
                      <Label htmlFor="newPassword">{t('security.newPassword')}</Label>
                      <Input
                        id="newPassword"
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="confirmPassword">{t('security.confirmPassword')}</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                      />
                    </div>
                    <Button type="submit" disabled={changingPassword}>
                      {changingPassword ? '...' : t('security.save')}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('security.notificationsTitle')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{t('security.emailNotificationsTitle')}</p>
                      <p className="text-sm text-muted-foreground">
                        {t('security.emailNotificationsDesc')}
                      </p>
                    </div>
                    <Badge variant="secondary">{t('security.enabled')}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{t('security.marketingEmailsTitle')}</p>
                      <p className="text-sm text-muted-foreground">
                        {t('security.marketingEmailsDesc')}
                      </p>
                    </div>
                    <Badge variant="outline">{t('security.disabled')}</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('security.deleteAccountTitle')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    {t('security.deleteAccountDescription')}
                  </p>
                  <Button variant="destructive">{t('security.deleteAccount')}</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
